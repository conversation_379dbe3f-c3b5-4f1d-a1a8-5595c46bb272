<template>
  <div class="dialog-overlay">
    <div class="dialog-container">
      <div class="dialog-header">
        <div class="dialog-title">精益求精</div>
        <div class="dialog-close" @click="$emit('close')">
          <DeleteIcon :size="24" color="var(--primary-color)" />
        </div>
      </div>
      <div class="dialog-content">
        <!-- 感谢信息 -->
        <div class="thank-message">
          感谢您的复盘建议！是否根据您的反馈重新生成一个更好的回答？
        </div>
        
        <!-- 复盘建议展示 -->
        <div class="suggestion-display">
          <div class="suggestion-title">您的复盘建议：</div>
          <div class="suggestion-content">{{ reviewSuggestion }}</div>
        </div>
        
        <!-- 说明信息 -->
        <div class="info-section">
          <div class="info-icon">💡</div>
          <div class="info-content">
            <div class="info-title">重新生成将结合：</div>
            <ul class="info-list">
              <li>原始问题和答案</li>
              <li>您的复盘建议</li>
              <li>相关背景资料</li>
              <li>最新搜索信息</li>
            </ul>
          </div>
        </div>
      </div>
      
      <!-- 底部按钮 -->
      <div class="dialog-footer">
        <button class="footer-button cancel-button" @click="$emit('close')">
          暂不重新生成
        </button>
        <button class="footer-button confirm-button" @click="handleConfirm">
          重新生成答案
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';

// Props定义
interface IProps {
  reviewSuggestion: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  confirm: [];
}>();

// 处理确认重新生成
const handleConfirm = () => {
  emit('confirm');
};
</script>

<style lang="scss" scoped>
// 对话框样式
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000; // 比第一个弹窗层级更高
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: var(--bg-glass-popup);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 650px;
  max-height: 80vh;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: rgba(00, 00, 00, 0.9);
    font-size: 40px;
    font-weight: 600;
  }

  .dialog-close {
    background: transparent;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--bg-glass-light);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
}

.thank-message {
  color: rgba(00, 00, 00, 0.9);
  font-size: 28px;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  padding: 16px;
  background: var(--bg-glass-light);
  border: 2px solid var(--border-accent);
  border-radius: 12px;
}

.suggestion-display {
  background: var(--bg-glass-light);
  border: 2px solid var(--border-accent);
  border-radius: 12px;
  padding: 20px;
  
  .suggestion-title {
    color: rgba(00, 00, 00, 0.9);
    font-size: 26px;
    font-weight: 600;
    margin-bottom: 12px;
  }
  
  .suggestion-content {
    color: rgba(00, 00, 00, 0.8);
    font-size: 24px;
    line-height: 1.5;
    background: rgba(255, 255, 255, 0.5);
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid var(--accent-color);
  }
}

.info-section {
  display: flex;
  gap: 16px;
  background: var(--primary-color-light);
  border: 2px solid var(--border-accent);
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid var(--accent-color);
  
  .info-icon {
    font-size: 32px;
    flex-shrink: 0;
  }
  
  .info-content {
    flex: 1;
    
    .info-title {
      color: rgba(00, 00, 00, 0.9);
      font-size: 26px;
      font-weight: 600;
      margin-bottom: 12px;
    }
    
    .info-list {
      margin: 0;
      padding-left: 20px;
      
      li {
        color: rgba(00, 00, 00, 0.8);
        font-size: 24px;
        line-height: 1.6;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--border-accent);
}

.footer-button {
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 26px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  
  &.cancel-button {
    background: var(--bg-glass-light);
    color: #000000;
    border-color: var(--border-accent);
    
    &:hover {
      background: rgba(255, 255, 255, 0.8);
    }
  }
  
  &.confirm-button {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    
    &:hover {
      background: var(--accent-color);
      border-color: var(--accent-color);
      transform: translateY(-2px);
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
