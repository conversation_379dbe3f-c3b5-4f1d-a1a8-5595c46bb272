<template>
  <div class="company-knowledge-container">
    <div class="memo-title">公司知识库</div>
    <div class="memo-content">
      <div ref="tagsContainer" class="memo-tags-container">
        <div v-if="knowledgeItems.length > 0" class="memo-tag">
          {{ knowledgeItems.length }}条公司相关知识
        </div>
        <div v-if="knowledgeItems.length === 0 && !isLoading" class="empty-memo">
          暂无知识库内容
        </div>
        <div v-if="isLoading" class="empty-memo">
          加载中...
        </div>
      </div>
    </div>
    <div class="memo-add-btn" @click="handleAddClick">
    </div>
  </div>

  <!-- 知识库弹窗 -->
  <KnowledgeDialog
    v-if="showKnowledgeDialog"
    @close="handleCloseDialog"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getKnowledgeList, type IKnowledgeItem } from '@/apis/knowledge';
import ExpandIcon from '@/assets/icons/ExpandIcon.vue';
import KnowledgeDialog from './KnowledgeDialog.vue';

// 响应式数据
const knowledgeItems = ref<IKnowledgeItem[]>([]);
const isLoading = ref(false);
const showKnowledgeDialog = ref(false);
const tagsContainer = ref<HTMLElement>();



// 获取知识库数据
const fetchKnowledgeItems = async () => {
  try {
    isLoading.value = true;
    console.log('📤 [CompanyKnowledgeBase] 开始获取知识库数据');

    const response = await getKnowledgeList({
      limit: 100,
      offset: 0,
    });

    if (response.result === 'success' && response.knowledge_items) {
      knowledgeItems.value = response.knowledge_items;
      console.log('✅ [CompanyKnowledgeBase] 知识库数据获取成功:', knowledgeItems.value);
    } else {
      console.warn('⚠️ [CompanyKnowledgeBase] 知识库数据获取失败或无数据');
      knowledgeItems.value = [];
    }
  } catch (error) {
    console.error('❌ [CompanyKnowledgeBase] 获取知识库数据失败:', error);
    knowledgeItems.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 处理添加按钮点击事件（打开弹窗）
const handleAddClick = () => {
  console.log('公司知识库添加按钮被点击，打开弹窗');
  showKnowledgeDialog.value = true;
};

// 处理关闭弹窗
const handleCloseDialog = () => {
  console.log('关闭知识库弹窗');
  showKnowledgeDialog.value = false;
};

// 组件挂载时获取数据
onMounted(() => {
  void fetchKnowledgeItems();
});
</script>

<style lang="scss" scoped>
.company-knowledge-container {
  height: 50px;
  width: 100%;
  background: #f7fdf7; // 更浅的绿色底
  border: 1px solid #2e7d32; // 与memo-tag颜色一致的边框
  border-radius: 12px;
  display: flex;
  align-items: center;
  padding: 2px 16px;
  box-sizing: border-box;
  position: relative;

  .memo-title {
    color: #2e7d32; // 与memo-tag颜色一致
    font-size: 26px;
    font-weight: 400; // 减小字体粗细
    white-space: nowrap;
    margin-right: 16px;
  }

  .memo-content {
    flex: 1;
    height: 100%;
    overflow: hidden;

    .memo-tags-container {
      display: flex;
      align-items: center;
      height: 100%;
      overflow-x: auto;
      overflow-y: hidden;
      gap: 12px;
      padding: 4px 0;

      // 隐藏滚动条但保持滚动功能
      scrollbar-width: none; // Firefox
      -ms-overflow-style: none; // IE/Edge
      &::-webkit-scrollbar {
        display: none; // Chrome/Safari
      }

      .memo-tag {
        background: #f1f8e9; // 比父组件略深的浅绿色底
        border-radius: 10px;
        padding: 8px 16px;
        font-size: 26px;
        color: #2e7d32;
        white-space: nowrap;
        flex-shrink: 0;
        border: 1px solid #a5d6a7;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        cursor: pointer;
      }

      .empty-memo {
        color: #81c784;
        font-size: 18px;
        font-style: italic;
        white-space: nowrap;
      }
    }
  }

  .memo-add-btn {
    width: 50px;
    height: 30px;
    background: transparent;
    border: 1px solid #2e7d32;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-left: 12px;
    color: #2e7d32;
    font-size: 16px;
    font-weight: 400;
    padding: 2px;

    &::after {
      content: "展开";
    }
  }
}
</style>
