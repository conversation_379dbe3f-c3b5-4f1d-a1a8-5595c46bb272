<template>
  <div class="dialog-overlay">
    <div class="dialog-container">
      <div class="dialog-header">
        <div class="dialog-title">回答复盘</div>
        <div class="dialog-close" @click="$emit('close')">
          <DeleteIcon :size="24" color="var(--primary-color)" />
        </div>
      </div>
      <div class="dialog-content">
        <!-- 便捷输入部分 -->
        <div class="quick-input-section">
          <div class="section-title">便捷输入</div>
          <div class="quick-buttons">
            <button
              v-for="option in quickOptions"
              :key="option.text"
              class="quick-button"
              @click="handleQuickInput(option.text)"
            >
              {{ option.text }}
            </button>
          </div>
        </div>
        
        <!-- 复盘建议输入框 -->
        <div class="review-input-section">
          <div class="section-title">复盘建议</div>
          <textarea
            v-model="reviewSuggestion"
            class="review-textarea"
            placeholder="请输入您的复盘建议..."
            rows="6"
          />
        </div>
      </div>
      
      <!-- 底部按钮 -->
      <div class="dialog-footer">
        <button class="footer-button cancel-button" @click="$emit('close')">
          取消
        </button>
        <button 
          class="footer-button submit-button" 
          :disabled="!reviewSuggestion.trim()"
          @click="handleSubmit"
        >
          提交复盘
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';

// Emits定义
const emit = defineEmits<{
  close: [];
  submit: [suggestion: string];
}>();

// 响应式数据
const reviewSuggestion = ref('');

// 便捷输入选项
const quickOptions = [
  { text: '回答更简短' },
  { text: '回答更详细' },
  { text: '搜索更多参考资料' },
  { text: '需要更多数据支撑' },
  { text: '时效性信息需要更新' },
  { text: '需要更多实例说明' },
];

// 处理便捷输入
const handleQuickInput = (text: string) => {
  if (reviewSuggestion.value.trim()) {
    reviewSuggestion.value = `${reviewSuggestion.value}；${text}`;
  } else {
    reviewSuggestion.value = text;
  }
};

// 处理提交
const handleSubmit = () => {
  if (reviewSuggestion.value.trim()) {
    emit('submit', reviewSuggestion.value.trim());
  }
};
</script>

<style lang="scss" scoped>
// 对话框样式
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: var(--bg-glass-popup);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 650px;
  max-height: 80vh;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: rgba(00, 00, 00, 0.9);
    font-size: 40px;
    font-weight: 600;
  }

  .dialog-close {
    background: transparent;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--bg-glass-light);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
}

.quick-input-section,
.review-input-section {
  .section-title {
    color: rgba(00, 00, 00, 0.9);
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 16px;
  }
}

.quick-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.quick-button {
  background: var(--bg-glass-light);
  border: 2px solid var(--border-accent);
  border-radius: 12px;
  padding: 12px 16px;
  color: #000000;
  font-size: 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: var(--primary-color-light);
    border-color: var(--accent-color);
    transform: translateY(-2px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.review-textarea {
  width: 100%;
  background: var(--bg-glass-light);
  border: 2px solid var(--border-accent);
  border-radius: 12px;
  padding: 16px;
  color: #000000;
  font-size: 24px;
  line-height: 1.5;
  resize: vertical;
  min-height: 120px;
  box-sizing: border-box;
  
  &::placeholder {
    color: rgba(0, 0, 0, 0.5);
  }
  
  &:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(139, 126, 216, 0.2);
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--border-accent);
}

.footer-button {
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 26px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  
  &.cancel-button {
    background: var(--bg-glass-light);
    color: #000000;
    border-color: var(--border-accent);
    
    &:hover {
      background: rgba(255, 255, 255, 0.8);
    }
  }
  
  &.submit-button {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    
    &:hover:not(:disabled) {
      background: var(--accent-color);
      border-color: var(--accent-color);
      transform: translateY(-2px);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
