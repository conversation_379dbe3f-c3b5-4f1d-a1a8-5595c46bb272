<template>
  <div class="industry-memo-container">
    <div class="memo-title">行业备忘录</div>
    <div class="memo-content">
      <!-- 内容区域暂时留空 -->
    </div>
    <div class="memo-add-btn" @click="handleAddClick">
    </div>
  </div>
</template>

<script setup lang="ts">
// Emits定义
const emit = defineEmits<{
  addIndustryMemo: [];
}>();

// 处理添加按钮点击事件
const handleAddClick = () => {
  console.log('行业备忘录添加按钮被点击');
  emit('addIndustryMemo');
};
</script>

<style lang="scss" scoped>
.industry-memo-container {
  height: 50px;
  width: 100%;
  background: #f3f9ff; // 更浅的蓝色底
  border: 1px solid #1565c0; // 更深的蓝色边框
  border-radius: 12px;
  display: flex;
  align-items: center;
  padding: 2px 16px;
  box-sizing: border-box;
  position: relative;
  .memo-title {
    color: #1565c0; // 更深的蓝色，与边框颜色统一
    font-size: 26px;
    font-weight: 400; // 减小字体粗细
    white-space: nowrap;
    margin-right: 16px;
  }

  .memo-content {
    flex: 1;
    height: 100%;
    // 内容区域暂时留空
  }

  .memo-add-btn {
    width: 50px;
    height: 30px;
    background: transparent;
    border: 1px solid #1565c0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #1565c0;
    font-size: 16px;
    font-weight: 400;
    padding: 2px;

    &::after {
      content: "添加";
    }
  }
}
</style>
